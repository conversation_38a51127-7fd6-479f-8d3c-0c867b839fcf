export const jobsData = [
  {
    id: 1,
    slug: "frontend-receeto",
    title: "Frontend Developer",
    company: "Receeto",
    duration: "3/2025 - 6/2025",
    logo: "/Receeto_logo.jpg",
    logoAlt: "Receeto Logo",
    summary: "A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.",
    roleOverview: "Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\n\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\n\nKey Highlights:\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\n\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\n\nPerformance Optimization:\n• Lazy loading for improved speed\n• Critical CSS and production build optimization\n\nTech Stack & Architecture:\n• Angular SPA with reactive state (signals)\n• Fully responsive design for mobile and desktop\n• Custom financial data visualizations using charts\n\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.",
    responsibilities: [
      "Develop responsive web applications using Angular and modern frontend technologies",
      "Implement financial data visualizations and interactive charts",
      "Optimize application performance through lazy loading and build optimization",
      "Create expense tracking and budgeting tools with real-time data processing",
      "Build responsive interfaces for both mobile and desktop platforms",
      "Implement Angular reactive state management using signals"
    ],
    skills: {
      "Frontend": ["Angular", "TypeScript", "RxJS", "Angular Signals", "Angular CLI"],
      "Styling": ["CSS3", "SASS/SCSS", "Angular Material", "Responsive Design", "Bootstrap"],
      "Tools & Testing": ["Git", "Angular CLI", "Webpack", "Lighthouse (for performance auditing)", "Figma"]
    },
    accomplishments: [
      {
        metric: "40%",
        description: "Improved application performance through lazy loading and build optimization"
      },
      {
        metric: "100%",
        description: "Responsive design compatibility across mobile and desktop platforms"
      },
      {
        metric: "NDA",
        description: "Confidential project delivered successfully while maintaining client privacy"
      }
    ],
    projects: [
      {
        title: "Project 1",
        description: "NDA - details confidential",
        image: "https://placehold.co/400x250/333333/ffffff?text=NDA+CONFIDENTIAL",
        technologies: ["Angular", "TypeScript", "Charts.js"]
      },
      {
        title: "Project 2",
        description: "NDA - details confidential",
        image: "https://placehold.co/400x250/333333/ffffff?text=NDA+CONFIDENTIAL",
        technologies: ["Angular", "RxJS", "Angular Material"]
      }
    ]
  },
  {
    id: 2,
    slug: "uiux-frontend-developer",
    title: "UI/UX Designer & Frontend Developer",
    company: "DigitalStudio Creative",
    duration: "2022 - 2023",
    logo: "https://via.placeholder.com/120x120/FF2D55/FFFFFF?text=DS",
    logoAlt: "DigitalStudio Creative Logo",
    summary: "Designed and developed responsive websites and mobile applications. Collaborated with clients to create compelling brand identities and user experiences that increased engagement by 40%.",
    roleOverview: "At DigitalStudio Creative, I bridged the gap between design and development, creating seamless user experiences from concept to implementation. My dual role allowed me to ensure design integrity throughout the development process while maintaining optimal performance and accessibility standards.",
    responsibilities: [
      "Design user interfaces and experiences for web and mobile applications",
      "Conduct user research and usability testing to inform design decisions",
      "Develop responsive frontend applications using modern frameworks",
      "Collaborate with clients to understand business requirements and user needs",
      "Create and maintain design systems and component libraries",
      "Optimize applications for performance and accessibility"
    ],
    skills: {
      "Design Tools": ["Figma", "Adobe XD", "Sketch", "Photoshop", "Illustrator"],
      "Frontend Development": ["React.js", "Vue.js", "JavaScript", "SASS/SCSS", "Bootstrap"],
      "UX Research": ["User Testing", "Wireframing", "Prototyping", "Analytics", "A/B Testing"]
    },
    accomplishments: [
      {
        metric: "40%",
        description: "Increase in user engagement through improved UX design"
      },
      {
        metric: "25+",
        description: "Successful client projects delivered on time and budget"
      },
      {
        metric: "95%",
        description: "Client satisfaction rate based on project feedback"
      },
      {
        metric: "3",
        description: "Design awards received for outstanding user experience"
      }
    ],
    projects: [
      {
        title: "Mobile Banking Application",
        description: "Designed and developed a secure, user-friendly mobile banking app with intuitive navigation",
        image: "https://via.placeholder.com/400x250/FF2D55/FFFFFF?text=Mobile+Banking+App",
        technologies: ["React Native", "Figma", "UX Research"]
      },
      {
        title: "Interactive E-Learning Platform",
        description: "Created engaging educational interface with gamification elements and progress tracking",
        image: "https://via.placeholder.com/400x250/4B0082/FFFFFF?text=E-Learning+Platform",
        technologies: ["Vue.js", "SCSS", "Adobe XD"]
      },
      {
        title: "Complete Brand Identity System",
        description: "Developed comprehensive brand guidelines and digital assets for startup company",
        image: "https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Brand+Identity+System",
        technologies: ["Illustrator", "Photoshop", "Brand Strategy"]
      }
    ]
  },
  {
    id: 3,
    slug: "junior-web-developer",
    title: "Junior Web Developer",
    company: "WebDev Agency",
    duration: "2021 - 2022",
    logo: "https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD",
    logoAlt: "WebDev Agency Logo",
    summary: "Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.",
    roleOverview: "As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.",
    responsibilities: [
      "Develop custom WordPress themes and plugins",
      "Build responsive websites using HTML, CSS, and JavaScript",
      "Create e-commerce solutions using WooCommerce and Shopify",
      "Collaborate with designers to implement pixel-perfect designs",
      "Optimize websites for performance and SEO",
      "Provide technical support and maintenance for client websites"
    ],
    skills: {
      "Frontend": ["HTML5", "CSS3", "JavaScript", "jQuery", "Bootstrap"],
      "Backend": ["PHP", "MySQL", "WordPress", "WooCommerce"],
      "Tools": ["Git", "Photoshop", "Chrome DevTools", "FTP", "cPanel"]
    },
    accomplishments: [
      {
        metric: "30+",
        description: "Websites successfully developed and launched"
      },
      {
        metric: "50%",
        description: "Improvement in page load speeds through optimization"
      },
      {
        metric: "100%",
        description: "Client satisfaction rate for delivered projects"
      }
    ],
    projects: [
      {
        title: "Restaurant Chain Website",
        description: "Built a multi-location restaurant website with online ordering system",
        image: "https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website",
        technologies: ["WordPress", "WooCommerce", "PHP"]
      },
      {
        title: "Real Estate Portal",
        description: "Developed property listing website with advanced search functionality",
        image: "https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal",
        technologies: ["HTML", "CSS", "JavaScript", "PHP"]
      }
    ]
  },
  {
    id: 4,
    slug: "freelance-designer",
    title: "Freelance Designer",
    company: "Self-Employed",
    duration: "2020 - 2021",
    logo: "https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL",
    logoAlt: "Freelance Logo",
    summary: "Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.",
    roleOverview: "Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.",
    responsibilities: [
      "Design logos and brand identities for small businesses",
      "Create marketing materials including flyers, brochures, and business cards",
      "Develop social media graphics and digital marketing assets",
      "Collaborate directly with business owners to understand their vision",
      "Manage multiple projects simultaneously while meeting deadlines",
      "Handle client communications and project billing"
    ],
    skills: {
      "Design Software": ["Adobe Illustrator", "Adobe Photoshop", "Adobe InDesign", "Canva"],
      "Design Skills": ["Logo Design", "Brand Identity", "Print Design", "Digital Graphics"],
      "Business Skills": ["Client Communication", "Project Management", "Time Management", "Pricing"]
    },
    accomplishments: [
      {
        metric: "20+",
        description: "Local businesses served with design solutions"
      },
      {
        metric: "4.9/5",
        description: "Average client rating on freelance platforms"
      },
      {
        metric: "90%",
        description: "Client retention rate for ongoing projects"
      }
    ],
    projects: [
      {
        title: "Local Coffee Shop Branding",
        description: "Complete brand identity including logo, menu design, and signage",
        image: "https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand",
        technologies: ["Illustrator", "Photoshop", "InDesign"]
      },
      {
        title: "Fitness Studio Marketing Kit",
        description: "Comprehensive marketing materials for new fitness studio launch",
        image: "https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing",
        technologies: ["Photoshop", "Illustrator", "Print Design"]
      }
    ]
  }
];
