[{"C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\JobDetail.js": "4", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Home.js": "5", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Header.js": "6", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Footer.js": "7", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\IntroCrafting.js": "8", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Intro.js": "9", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\SkillsTicker.js": "10", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Statistics.js": "11", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Experience.js": "12", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Portfolio.js": "13", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\ClientThoughts.js": "14", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Contact.js": "15", "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\data\\jobsData.js": "16"}, {"size": 535, "mtime": 1750677780777, "results": "17", "hashOfConfig": "18"}, {"size": 362, "mtime": 1750677781148, "results": "19", "hashOfConfig": "18"}, {"size": 478, "mtime": 1750678343747, "results": "20", "hashOfConfig": "18"}, {"size": 4241, "mtime": 1750678692108, "results": "21", "hashOfConfig": "18"}, {"size": 710, "mtime": 1750678648213, "results": "22", "hashOfConfig": "18"}, {"size": 632, "mtime": 1750678409761, "results": "23", "hashOfConfig": "18"}, {"size": 378, "mtime": 1750679469060, "results": "24", "hashOfConfig": "18"}, {"size": 3068, "mtime": 1750679487217, "results": "25", "hashOfConfig": "18"}, {"size": 259, "mtime": 1750678426781, "results": "26", "hashOfConfig": "18"}, {"size": 843, "mtime": 1750678483051, "results": "27", "hashOfConfig": "18"}, {"size": 786, "mtime": 1750679527155, "results": "28", "hashOfConfig": "18"}, {"size": 1190, "mtime": 1750678524120, "results": "29", "hashOfConfig": "18"}, {"size": 5313, "mtime": 1750679512484, "results": "30", "hashOfConfig": "18"}, {"size": 750, "mtime": 1750678580817, "results": "31", "hashOfConfig": "18"}, {"size": 1289, "mtime": 1750678625297, "results": "32", "hashOfConfig": "18"}, {"size": 10691, "mtime": 1750678187559, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4zorhp", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\JobDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\IntroCrafting.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Intro.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\SkillsTicker.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Statistics.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Experience.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Portfolio.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\ClientThoughts.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\components\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfulio\\portfolio-react\\src\\data\\jobsData.js", [], []]